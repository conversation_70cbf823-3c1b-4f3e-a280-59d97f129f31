parameters:
  # Common
  - name: environment
  - name: taskcondition
    default: succeeded()

  # AWS Auth
  - name: awsServiceConnectionName
  - name: awsRegion

  # Terraform configuration
  - name: terraformProjectLocation
  - name: terraformGlobalTFVars
  - name: terraformLogLevel
    default: 'INFO'

  # Terraform CLI options
  - name: terraformCLIGlobalOptionsForPlan
  - name: terraformCLIOptionsForPlan

  # Plan options
  - name: planFilePath
    default: 'tfplan.out'
  - name: artifactname
    default: 'tfplan'

  # Not exposed options
  - name: terraformDestroyPlan
    type: boolean
    default: false
  - name: tfvarsToUseFromPipelineVariables
    type: object
    default: {}

### Plan steps
steps:

  # Terraform plan
  - task: AWSShellScript@1
    ${{ if ne(parameters.artifactname, 'tfplandestroy') }}: 
      displayName: Terraform plan
    ${{ else }}:
      displayName: Generate destroy plan
    name: plan
    condition: ${{ parameters.taskcondition }}
    env:
      TF_DESTROY_PLAN: ${{ lower(parameters.terraformDestroyPlan) }}
      ${{ each var in parameters.tfvarsToUseFromPipelineVariables }}:
        TF_VAR_${{ var.tfvarName }}: ${{ var.varName }}
    inputs:
      awsCredentials: ${{ parameters.awsServiceConnectionName }}
      regionName: ${{ parameters.awsRegion }}
      scriptType: inline
      workingDirectory: "$(Pipeline.Workspace)/source_repo/${{ parameters.terraformProjectLocation }}"
      inlineScript: |
        set -eu  # fail on error
        export TF_LOG=${{ parameters.terraformLogLevel}}
        TFVARS_PARAMS=""
        trap "echo Error on line $LINENO" ERR

        # Set up tfvars parameters
        if [ -f "$(Pipeline.Workspace)/config_repo/${{ parameters.terraformProjectLocation }}/${{ parameters.environment }}.tfvars" ]; then
          TFVARS_PARAMS="$TFVARS_PARAMS -var-file=$(Pipeline.Workspace)/config_repo/${{ parameters.terraformProjectLocation }}/${{ parameters.environment }}.tfvars"
        fi

        if [ -n "${{ parameters.terraformGlobalTFVars }}" ]; then
          TFVARS_PARAMS="$TFVARS_PARAMS -var-file=${{ parameters.terraformGlobalTFVars }}"
        fi

        # Set up destroy plan arguments
        TF_EXTRA_ARGS=""
        if [ "$TF_DESTROY_PLAN" = "true" ]; then
          TF_EXTRA_ARGS="-destroy"
        fi

        echo "Running terraform plan with the following parameters:"
        echo "TFVARS_PARAMS: $TFVARS_PARAMS"
        echo "TF_EXTRA_ARGS: $TF_EXTRA_ARGS"

        set +e
        terraform ${{ parameters.terraformCLIGlobalOptionsForPlan }} plan \
          -detailed-exitcode \
          $TFVARS_PARAMS \
          -out="$(Build.SourcesDirectory)/${{ parameters.planFilePath }}" $TF_EXTRA_ARGS ${{ parameters.terraformCLIOptionsForPlan }}
        retVal=$?
        set -e
        if [ $retVal -eq 2 ]; then
            echo '##vso[task.setvariable variable=terraform_changes_to_apply;isOutput=true]true'
            terraform ${{ parameters.terraformCLIGlobalOptionsForPlan }} show -json "$(Build.SourcesDirectory)/${{ parameters.planFilePath }}" | jq '.' > $(Build.SourcesDirectory)/tfplan.json
            terraform ${{ parameters.terraformCLIGlobalOptionsForPlan }} show -no-color "$(Build.SourcesDirectory)/${{ parameters.planFilePath }}" > $(Build.SourcesDirectory)/tfplan.txt
        elif [ $retVal -eq 1 ]; then
            echo "Terraform plan failed"
            exit 1
        else
            echo '##vso[task.setvariable variable=terraform_changes_to_apply;isOutput=true]false'
            echo "No changes detected"
        fi

  # Publish terraform plan
  - task: PublishPipelineArtifact@1
    displayName: "Publish Terraform plan pipeline artifact"
    condition: ${{ parameters.taskcondition }}
    inputs:
      targetPath: "$(Build.SourcesDirectory)/${{ parameters.planFilePath }}"
      artifact: ${{ parameters.artifactname }}

  # Publish terraform plan JSON (if changes detected)
  - task: PublishPipelineArtifact@1
    displayName: "Publish Terraform plan JSON pipeline artifact"
    condition: and(${{ parameters.taskcondition }}, eq(variables['plan.terraform_changes_to_apply'], 'true'))
    inputs:
      targetPath: "$(Build.SourcesDirectory)/tfplan.json"
      artifact: ${{ parameters.artifactname }}-json

  # Publish terraform plan text (if changes detected)
  - task: PublishPipelineArtifact@1
    displayName: "Publish Terraform plan text pipeline artifact"
    condition: and(${{ parameters.taskcondition }}, eq(variables['plan.terraform_changes_to_apply'], 'true'))
    inputs:
      targetPath: "$(Build.SourcesDirectory)/tfplan.txt"
      artifact: ${{ parameters.artifactname }}-txt
